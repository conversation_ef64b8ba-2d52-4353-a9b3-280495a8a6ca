var Sequelize = require('sequelize')
const { Op, fn, col, where } = Sequelize
const moment = require('moment')
const sequelize = require('sequelize')

const bar = require('../../models/bar')
var env = require('../../config/environment')
var jwt = require('jsonwebtoken')

const ads = require('../../models/ads')
const segmentUserVenue = require('../../models/segmentUserVenue')
const ads_segment = require('../../models/ads_segment')
const segment = require('../../models/segment')
const advertiserUserModel = require('../../models/advertiser_user')
const userAdsSaveModel = require('../../models/userAdsSave')
const userAdsAnalyticModel = require('../../models/userAdsAnalytic')
const message = require('../../config/message')

exports.advertiserList = async (req, res) => {
  try {
    const { page = 1, limit = 100, search } = req.body;
    const offset = (page - 1) * limit;

    // Initialize whereClause as an object
    const whereClause = {      
      ad_status: 'Approved',
      pause_status: false,
      deleted_at: null
    };

    // 3. Ads active check - start date/time and end date/time
    const currentDate = moment().format('YYYY-MM-DD');
    const currentTime = moment().format('HH:mm:ss');

    whereClause[Op.and] = [
      {
        [Op.and]: [
          where(fn('DATE', col('start_date')), '<=', currentDate),
          where(fn('DATE', col('end_date')), '>=', currentDate)
        ]
      },
      // {
      //   [Op.and]: [
      //     where(fn('TIME', col('start_time')), '<=', currentTime),
      //     where(fn('TIME', col('end_time')), '>=', currentTime)
      //   ]
      // }
    ];

    // Add search condition if provided
    if (search && search.trim()) {
      whereClause[Op.or] = [
        { ad_title: { [Op.like]: `%${search.trim()}%` } },
        { ad_description: { [Op.like]: `%${search.trim()}%` } }
      ];
    }

    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID;

    /*All mytab customers ads*/
    // const adsData = await ads.findAndCountAll({
    const all_mytab_customers_ads = await ads.findAll({
      attributes: [
        'id',
        'ad_title',
        'ad_description',
        'media_url',
        'call_to_action',
        'call_to_action_url',
        'ad_status',
        'pause_status',
        [Sequelize.literal(`CONCAT(DATE_FORMAT(ads.start_date, '%Y-%m-%d'), ' ', TIME_FORMAT(ads.start_time, '%H:%i:%s'))`), 'start_date_time'],
        [Sequelize.literal(`CONCAT(DATE_FORMAT(ads.end_date, '%Y-%m-%d'), ' ', TIME_FORMAT(ads.end_time, '%H:%i:%s'))`), 'end_date_time'],
        'start_time',
        'end_time',
        'created_by_type',
        'created_by_id',
        [Sequelize.literal(`(SELECT COUNT(*) FROM user_ads_save WHERE user_ads_save.adsID = ads.id AND user_ads_save.userID != ${userID})`), 'saved_count'],
        [Sequelize.literal(`EXISTS (SELECT 1 FROM user_ads_save WHERE user_ads_save.adsID = ads.id AND user_ads_save.userID = ${userID})`), 'user_has_saved'],
        [Sequelize.literal(`CONCAT_WS(', ', ads.suburb, ads.city, ads.state)`), 'ads_address']
      ],
      where: {...whereClause,eligibility_type: 'all_mytab_customers'},
      group: ['ads.id', 'venue.id', 'advertiser_user.id'],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
      subQuery: false,
      include: [
        {
          model: bar,
          as: 'venue',
          required: false,
          attributes: ['id', 'restaurantName', 'address', 'avatar','serviceType'],
          where: { '$ads.created_by_type$': 'venue' }
        },
        {
          model: advertiserUserModel,
          as: 'advertiser_user',
          required: false,
          attributes: ['id', 'profile_image', 'contact_name', 'email'],
          where: { '$ads.created_by_type$': 'advertiser' }
        },
        {
          model: userAdsSaveModel,
          as: 'user_ads_save',
          attributes: [],
          required: false
        }
      ]
    });

    /*All mytab_customer_segments ads*/
    const mytab_customer_segments = await segmentUserVenue.findAll({      
      where: { userID: userID },
      include: [{
        model: segment,
        required: true,
        include: {
          model: ads_segment,
          required: true,
          include: {
            model: ads,
            required: true,
            attributes: [
              'id',
              'ad_title',              
              [Sequelize.literal(`CONCAT(DATE_FORMAT(start_date, '%Y-%m-%d'), ' ', TIME_FORMAT(start_time, '%H:%i:%s'))`), 'start_date_time'],
              [Sequelize.literal(`CONCAT(DATE_FORMAT(end_date, '%Y-%m-%d'), ' ', TIME_FORMAT(end_time, '%H:%i:%s'))`), 'end_date_time'],
              [Sequelize.literal(`CONCAT_WS(', ', suburb, city, state)`), 'ads_address'],
              'ad_description',
              'media_url',
              'call_to_action',
              'call_to_action_url',
              'ad_status',
              'pause_status',
              'created_by_type',
              'created_by_id',
              // [Sequelize.literal(`(SELECT COUNT(*) FROM user_ads_save AS uas WHERE uas.adsID = \`segment.ads_segments.ad.id\`)`), 'saved_count'],
              [Sequelize.literal(`(SELECT COUNT(*) FROM user_ads_save AS uas WHERE uas.adsID = \`segment.ads_segments.ad.id\` AND uas.userID != ${userID})`), 'saved_count'],
              [Sequelize.literal(`EXISTS (SELECT 1 FROM user_ads_save AS uas WHERE uas.adsID = \`segment.ads_segments.ad.id\` AND uas.userID = ${userID})`), 'user_has_saved']
            ],
            where: {
              ...whereClause,
              [Sequelize.Op.or]: [
                { created_by_type: 'venue' },
                { created_by_type: 'advertiser' }
              ]
            },
            include: [
              {
                model: bar,
                as: 'venue',
                required: false,
                attributes: ['id', 'restaurantName', 'address', 'avatar','serviceType'],
              },
              {
                model: advertiserUserModel,
                as: 'advertiser_user',
                required: false,
                attributes: ['id', 'profile_image', 'contact_name', 'email'],
              }
            ]
          }
        }
      }]
    });

    const mytab_customer_segments_list = mytab_customer_segments.flatMap(item => item.segment?.ads_segments?.map(adSegment => adSegment.ad) || []);
    // Optional: remove duplicates by `id`
    const unique_mytab_customer_segments_list = Object.values(
      mytab_customer_segments_list.reduce((acc, ad) => {
        acc[ad.id] = ad;
        return acc;
      }, {})
    );


    /*Combined both object with unique*/
    const combined = [...all_mytab_customers_ads, ...unique_mytab_customer_segments_list];
    const uniqueAdss = Object.values(
      combined.reduce((acc, ad) => {
        acc[ad.id] = ad;  // Overwrites duplicate `id`
        return acc;
      }, {})
    );

    res.status(200).send({
      success: 1,
      message: 'advertiser list retrieve successfully',
      data: uniqueAdss,
    })

  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
},
  exports.saveUserAds = async (req, res) => {
    try {
      const { id, flag } = req.body;
      const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
      const userID = sessionData.userID;

      if (!id || flag === 'undefined' || flag === null) {
        return res.status(400).send({
          success: 0,
          message: message.ads.PAUSEMESSINGREQUIRED
        });
      }

      if (flag === true) {
        // Save ad
        await userAdsSaveModel.findOrCreate({
          where: {
            adsID: id,
            userID: userID
          }
        });

        return res.status(200).send({
          success: 1,
          message: "Ad saved successfully."
        });
      } else {
        // Unsave ad
        await userAdsSaveModel.destroy({
          where: {
            adsID: id,
            userID: userID
          }
        });

        return res.status(200).send({
          success: 1,
          message: "Ad unsaved successfully."
        });
      }
    } catch (error) {
      console.error(error); // Optional: for debugging
      return res.status(500).send({
        success: 0,
        message: "Internal server error."
      });
    }
  },
  exports.userAdsClick = async (req, res) => {
    try {
      const { id } = req.body;
      const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
      const userID = sessionData.userID;

      if (!id) {
        return res.status(400).send({
          success: 0,
          message: "Ad ID is required."
        });
      }

      // Try to find existing record
      let stat = await userAdsAnalyticModel.findOne({ where: { userID, 'adsID': id } });

      if (stat) {
        if (stat.clicks === 0) {
          // Allow first click only
          await stat.update({
            clicks: 1,
            last_click_at: new Date()
          });
          return res.status(200).send({
            success: 1,
            message: "Click recorded"
          });
        } else {
          // Already clicked
          return res.status(200).send({
            success: 1,
            message: "User already clicked this ad"
          });
        }
      } else {
        // First time interaction
        await userAdsAnalyticModel.create({
          userID,
          adsID: id,
          clicks: 1,
          last_click_at: new Date()
        });
        return res.status(200).send({
          success: 1,
          message: "Click recorded (first time)"
        });
      }
    } catch (error) {
      console.error(error); // Optional: for debugging
      return res.status(500).send({
        success: 0,
        message: "Internal server error."
      });
    }
  },
  exports.userAdsImpression = async (req, res) => {
    try {
      const { id } = req.body;
      const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
      const userID = sessionData.userID;

      if (!id) {
        return res.status(400).send({
          success: 0,
          message: "Ad ID is required."
        });
      }

      // Try to find existing record
      let existing = await userAdsAnalyticModel.findOne({ where: { userID, 'adsID': id } });

      if (existing) {
        // Increment impressions count
        await existing.increment('impressions');
        await existing.update({ last_impression_at: new Date() });
      } else {
        // First-time impression
        await userAdsAnalyticModel.create({
          userID,
          adsID: id,
          impressions: 1,
          last_impression_at: new Date(),
        });
      }
      return res.status(200).send({
        success: 1,
        data: existing,
        message: "Impression recorded"
      });
    } catch (error) {
      console.error(error); // Optional: for debugging
      return res.status(500).send({
        success: 0,
        message: "Internal server error."
      });
    }
  }